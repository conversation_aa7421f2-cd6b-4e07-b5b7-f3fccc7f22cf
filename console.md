 [TOKEN MANAGER] Token Manager inizializzato
 [STATE] Dati caricati da sessionStorage e rimossi
 [STATE] Controllo iniziale:
 [STATE] - skipAnimations: true
 [STATE] - fullInterface: true
 [STATE] - fromSessionStorage: true
 [STATE] - savedState: null
 [STATE] - shouldShowFullInterface: true
 [STATE] - hasActiveGame: false
 [STATE] - shouldDefaultToFullInterface: false
 [GAME] Dati da sessionStorage: attivo interfaccia completa
 [DRAG FIX] Removed setup-animation element from DOM
 [SOCKET INIT] Creating socket with token: Present
 [SOCKET HANDLERS] Handler socket aggiuntivi inizializzati
 [LOCAL GAME] Manager caricato e pronto
 [ONLINE GAME] Manager caricato e pronto
 [GAME MODE] Manager principale caricato e pronto
 [SOCKET CONNECT] myPlayerId attuale: Mz2eoSO3Hwkb-jYCAAAc socket.id: Mz2eoSO3Hwkb-jYCAAAc
 [CHAT] DEBUG: Registrando event listener per chatMessage su socket connesso. Socket ID: Mz2eoSO3Hwkb-jYCAAAc
 [CHAT] Event listener per chatMessage registrato su socket connesso
 [CHAT] DEBUG: Numero di listener per chatMessage: 1
 [NAMES PROTECTION] Sistema di protezione nomi caricato
 [SMOOTH LOADING] Inizializzazione sistema loading fluido
 [SMOOTH LOADING] Sistema di loading fluido caricato
 [GAME] DOM caricato, mostro interfaccia completa
 [GAME] Game container mostrato con interfaccia completa
 [STATE] Stato salvato: Object
 [STATE] Dati da sessionStorage, URL già pulito
 [PRELOAD] Avvio precaricamento immagini delle carte: Array(4)
 [PLAYER NAMES] Modalità locale - Nome del giocatore 1 impostato a: giggio
 [INIT] Pagina di gioco rilevata, evito fullResetGameUI per prevenire refresh continui
 [PSN] Inizializzazione sistema PSN...
 [MULTIPLAYER] DOMContentLoaded - Inizio inizializzazione
 [MULTIPLAYER] URL: http://localhost:3000/game
 [CHAT] DEBUG: Chat event listener sarà registrato nell'evento connect quando socket è connesso
 [CHAT] DEBUG: Socket ID in initSocketEvents: Mz2eoSO3Hwkb-jYCAAAc (potrebbe essere undefined)
 [CHAT] DEBUG: Socket presente ma potrebbe non essere ancora connesso. Socket ID: Mz2eoSO3Hwkb-jYCAAAc
 [CHAT] Event listener per chatMessage sarà registrato quando il socket è connesso
 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
 [HAND SLOTS] Creati slot per la mano del giocatore 1
 [HAND SLOTS] Creati slot per la mano del giocatore 2
 [ANIMATION] Osservatore impostato per rilevare il completamento dell'animazione
 [ONLINE ENHANCER] DOM pronto, inizializzo enhancer...
 victory-fix.js caricato.
 [PRELOAD] SUCCESSO precaricamento 1/4: http://localhost:3000/img/carte/card-back.webp
 [PRELOAD] SUCCESSO precaricamento 4/4: http://localhost:3000/img/Cover carte/cover.png
 [PRELOAD] SUCCESSO precaricamento 2/4: http://localhost:3000/img/carte/card-back.png
 [PRELOAD] SUCCESSO precaricamento 3/4: http://localhost:3000/img/Cover carte/cover.webp
 [BOARD CREATION] Creazione nuovo tabellone di gioco - Stack trace: Error
    at createGameBoard (http://localhost:3000/script.js:5245:87)
    at http://localhost:3000/js/game-state-manager.js:127:21
 [GAME] Tabellone di gioco creato
 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
 [HAND SLOTS] Creati slot per la mano del giocatore 1
 [HAND SLOTS] Creati slot per la mano del giocatore 2
 [GAME] Slot mani giocatori creati
 [SMOOTH LOADING] Avvio animazioni di caricamento
 [SMOOTH LOADING] Animazioni di caricamento completate
 [GAME] Tab "Nuova Partita" attivato
 [GAME] Contenuto "Nuova Partita" mostrato
 Could not access stylesheet rules: 
(anonime) @ inspector.b9415ea5.js:12
 Ads initialization already in progress or completed
 Ads initialization already in progress or completed
 Ads initialization already in progress or completed
 
s7 @ inspector.b9415ea5.js:1
inspector.b9415ea5.js:1 Uncaught 
 Attempting to initialize AdUnit
 AdUnit initialized successfully
 Ads initialized successfully for: http://localhost:3000/game
 Ads initialization already in progress or completed
 [ONLINE ENHANCER] Finestra completamente caricata
 [ONLINE ENHANCER] Pulsante Gioca Online non cliccato, non applico stili al caricamento
 [PERSISTENCE] Stato salvato scaduto o non presente
 [PERSISTENCE] Stato salvato rimosso
 [NAMES PROTECTION] Salvato nome player 1: Giocatore 1
 [NAMES PROTECTION] Salvato nome player 2: Giocatore 2
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: Giocatore 1
 [NAMES PROTECTION] Salvato nome player 2: Giocatore 2
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
game:1 Uncaught (in promise) 
game:1 Uncaught (in promise) 
game:1 Uncaught (in promise) 
game:1 Uncaught (in promise) 
game:1 Uncaught (in promise) 
game:1 Uncaught (in promise) 
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [VISIBILITY] Flag setup interrotto resettato
 [GAME] Pulsante Nuova Partita cliccato
 [GAME] Modalità: online Tempo: none
 [MATCHMAKING] Avvio matchmaking online
 [MATCHMAKING] Socket già connesso
 [MATCHMAKING] Socket connesso, invio findMatch
 [MATCHMAKING] Invio findMatch con dati: Object
 [SMOOTH LOADING] Gestendo transizione matchmaking
 [MATCHMAKING] Match trovato! Object
 [MATCHMAKING] Salvando dati partita con gameId: K10R8L
 [STATE] Pulizia stato
 [MATCHMAKING] Stato interfaccia completa pulito
 [STATE] Partita iniziata, pulizia stato interfaccia completa
 [STATE] Dettagli partita: Object
 [STATE] Pulizia stato
 [MATCHMAKING] Match trovato - il flusso delle animazioni inizierà automaticamente senza refresh
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: giggio
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: bruscolino
 [PLAYER AREAS] Attivata animazione glow aggiungendo classe ready-for-play
 [GAME STATE] Prima chiamata a showGameSetup
 [GAME STATE] Partita multiplayer - preparazione setup e inizio animazioni
 [GAME SETUP] showGameSetup() chiamato
 [GAME SETUP] Modalità transizione fluida - evito reset visuale
 [GAME SETUP] Transizione fluida - skip visualizzazione container
 [GAME STATE] Partita online - procedendo con le animazioni...
 [GAME SETUP] showGameSetup() chiamato
 [GAME SETUP] Preparando game container per partita locale
 [GAME SETUP] Classe ready-for-play già presente, salto l'aggiunta
 [GAME SETUP] Tabellone esistente trovato, aggiorno rendering
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [GAME SETUP] Completato
 [DICE ANIMATION] animateDiceRoll chiamato - Animazione dei dadi commentata/disabilitata
 [DICE ANIMATION] Parametri: Object
 [DICE ANIM] Saltando animazione dadi, usando diceResult: Object e initialPosition: a4
 [DICE ANIM] Usando initialPosition (a4) -> Alpha: A, Numeric: 4
 [DICE ANIM] Dati dadi configurati:
 [DICE ANIM] Dado numerico mostrerà: 4
 [DICE ANIM] Dado alfabetico mostrerà: A
 [DICE ANIM] Dado colore mostrerà: black
 [DICE ANIM] initialPosition: a4
 [DICE ANIM] Saltata animazione dei dadi
 [DICE ANIM] Flag diceAnimationCompletedOnce impostato dopo salto animazione
 [ANIMATION] Rilevata classe ready-for-play sul game-container, avvio i timer
 [ANIMATION] Flag globale gameContainerObserverTriggered impostato a true
 [ANIMATION] window.currentGameState presente, riapplico lo stato
 [ANIMATION] Observer disconnesso e rimosso
 [ANIMATION] Rilevata classe ready-for-play sul game-container, avvio i timer
 [ANIMATION] Flag globale gameContainerObserverTriggered impostato a true
 [ANIMATION] window.currentGameState presente, riapplico lo stato
 [ERROR] Errore JavaScript rilevato: 
(anonime) @ multiplayer.js:1759
 [ERROR] Messaggio: Uncaught TypeError: Cannot read properties of null (reading 'disconnect')
(anonime) @ multiplayer.js:1760
 [ERROR] File: http://localhost:3000/js/multiplayer.js
(anonime) @ multiplayer.js:1761
 [ERROR] Linea: 1921
(anonime) @ multiplayer.js:1762
 [ERROR] Stack: TypeError: Cannot read properties of null (reading 'disconnect')
    at http://localhost:3000/js/multiplayer.js:1921:46
    at Array.forEach (<anonymous>)
    at MutationObserver.<anonymous> (http://localhost:3000/js/multiplayer.js:1897:19)
(anonime) @ multiplayer.js:1763
multiplayer.js:1921 Uncaught 
 [ANIMATION] Riapplico lo stato di gioco per avviare i timer dopo l'animazione
 [ANIMATION] Riapplico lo stato di gioco per avviare i timer dopo l'animazione
 [SIDEBAR] Riabilitazione di tutti i tab
 [SIDEBAR] Tab riabilitato: gioca
 [SIDEBAR] Tab riabilitato: nuova-partita
 [SIDEBAR] Tab riabilitato: analisi
 [SIDEBAR] Tab riabilitato: giocatori
 [SIDEBAR] Passaggio al tab Gioca
 [SIDEBAR] Tab "Gioca" attivato
 [SIDEBAR] Contenuto "Gioca" mostrato
 [MATCHMAKING] Passaggio al tab Gioca completato
 [GAME STATE] Preparando transizione fluida da setup a game container
 [GAME STATE] Preparazione interfaccia multiplayer con pre-rendering
 [GAME STATE] Game container pre-renderizzato e pronto per fade-in
 [GAME STATE] Setup-animation non trovato
 [GAME STATE] Mostrando game container dopo preparazione
 [DICE ANIMATION] showGameContainer() chiamato
 [DICE ANIMATION] Nascondo dice animation area con fade-out
 [DICE ANIMATION] Nascondo dice animation overlay
 [SHOWGAME] Nomi già impostati definitivamente, preservo i valori attuali
 [SHOWGAME] Game container finale: Object
 [SHOWGAME DEBUG] Celle esistenti: 36 Dovrebbe creare tabellone: false
 [SHOWGAME] Tabellone già esistente, skip creazione
 [RESET UI] Tabellone già esistente con 36 celle, skip ricreazione
 [RESET UI] Reset memorizzazione permanente nomi e colori
 [RESET UI] Reset flag animazione dadi
 [RESET UI] Reset posizione iniziale originale
 [ONLINE ENHANCER] Evento gameContainerShown ricevuto
 [ONLINE ENHANCER] Pulsante Gioca Online non cliccato, non applico stili
 [GAME STATE] Avvio animazioni carte dopo i dadi
 [NAMES PROTECTION] animateCardDealing intercettato
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [CARD DEALING] Deck area mostrata
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: giggio
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: 
 [NAMES PROTECTION] BLOCCATO svuotamento player2, mantengo: bruscolino
 [NAMES PROTECTION] renderHand intercettato, cards: 0
 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] renderHand intercettato, cards: 0
 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Protezione attivata
 [NAMES PROTECTION] Salvato nome player 1: giggio
 [NAMES PROTECTION] Salvato nome player 2: bruscolino
 [NAMES PROTECTION] Tentativo di modificare player1 textContent: giggio
 [NAMES PROTECTION] Tentativo di modificare player2 textContent: bruscolino
 [DECK IMAGE] Mazzo caricato con successo: http://localhost:3000/img/carte/card-back.webp
 [GAME STATE] Reset flag processingFirstOnlineState
 [DICE STATUS DEBUG] Tentativo strategia 2 - variabili globali
 [DICE STATUS DEBUG] Dopo strategia 2 - player1Name: ... player2Name: ...
 [DICE STATUS DEBUG] Tentativo strategia 3 - DOM fallback
 [DICE STATUS DEBUG] Trovato player1Name dal DOM: giggio
 [DICE STATUS DEBUG] Trovato player2Name dal DOM: bruscolino
 [DICE STATUS DEBUG] Dopo strategia 3 - player1Name: giggio player2Name: bruscolino
 [DICE STATUS] Aggiornato stato dadi: A4 - Bianco: giggio, Nero: bruscolino
 [ANIM] Avvio animazione carta iniziale: Object su a4
 [ANIM] Inizio animazione movimento carta iniziale.
 [ANIM] Fine animazione movimento. Rimuovo cover e mostro carta reale.
 [ANIM] Utilizzo fallback setTimeout per animazione finale
 [ANIM] Fine animateInitialCardPlacement con fallback.
 [ANIMATION DEBUG] Verifica condizioni per animazione nomi: Object
 [ANIMATION] Animando con G1 (Bianco): giggio vs G2 (Nero): bruscolino
 [ANIMATION] Convenzione standard: Bianco in alto, Nero in basso
 [PLAYER NAMES ANIMATION] ===== FUNZIONE CHIAMATA =====
 [PLAYER NAMES ANIMATION] Avvio animazione nomi giocatori DIRETTA
 [PLAYER NAMES ANIMATION] Nomi forniti: giggio vs bruscolino
 [PLAYER NAMES ANIMATION] Usando nomi forniti direttamente: giggio vs bruscolino
 [PLAYER NAMES ANIMATION] Nomi giocatori finali: giggio vs bruscolino
 [PLAYER NAMES ANIMATION] Game board trovato: true
 [PLAYER NAMES ANIMATION] Game board dimensioni: DOMRect
 [PLAYER NAMES ANIMATION] Animazione creata dinamicamente e aggiunta al DOM
 [PLAYER NAMES ANIMATION] Container aggiunto al game-board: dynamic-names-animation
 [PLAYER NAMES ANIMATION] Game-board children count: 41
 [PLAYER NAMES ANIMATION] Terminando animazione
 [PLAYER NAMES ANIMATION] Animazione completata e rimossa
 [PLAYER NAMES ANIMATION] Fade-in completato
 [PLAYER NAMES ANIMATION] isSetupAnimating resettato a false alla fine reale dell'animazione
 [PLAYER NAMES ANIMATION] nameAnimationCompleted impostato a true - animazione nomi completata
 [PLAYER NAMES ANIMATION] Forzando re-rendering delle carte dopo reset isSetupAnimating...
 [SETUP RE-RENDER] Nessuno stato di gioco disponibile per re-rendering
script.js:11510 [ANIMATION] Animazioni completate e marcate per gameId: K10R8L
script.js:11511 [ANIMATION] isSetupAnimating sarà resettato da animatePlayerNames() alla fine reale dell'animazione
script.js:11514 [ANIMATION] Chiamando handleGameState con animazioni completate
script.js:11919 [TURN PROTECTION] 🏁 Inizializzato wasInSetupPhase: false
script.js:11968 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
script.js:11969 [TURN PROTECTION] Turno era protetto: Mz2eoSO3Hwkb-jYCAAAc
game-mode-manager.js:62 [GAME MODE] Manager inizializzato
game-mode-manager.js:104 [GAME MODE] Processando stato online senza manager attivo (normale durante inizializzazione)
script.js:12166 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12169 [GAME STATE] -otOSPYYXNrIK0pdAAAr (black): Array(5)
script.js:12169 [GAME STATE] Mz2eoSO3Hwkb-jYCAAAc (white): Array(5)
script.js:12389 [PERMANENT NAMES] Nomi permanenti finali: {"-otOSPYYXNrIK0pdAAAr":"bruscolino","Mz2eoSO3Hwkb-jYCAAAc":"giggio"}
script.js:12411 [PERMANENT COLORS] Colori permanenti finali: {"-otOSPYYXNrIK0pdAAAr":"black","Mz2eoSO3Hwkb-jYCAAAc":"white"}
psn-unified.js:109 [PSN API] Aggiornamento handSize dai dati del server
psn-unified.js:122 [PSN API] Aggiornato handSize nero: 5
psn-unified.js:119 [PSN API] Aggiornato handSize bianco: 5
script.js:12437 [EARLY PLAYER2 MONITOR] Observer installato il prima possibile, stato attuale: bruscolino
script.js:12444 [EARLY RESTORE] Controllo nomi: Object
script.js:12483 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12484 [ANIMATION DEBUG] - isStarting: false
script.js:12485 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12486 [ANIMATION DEBUG] - state.gameId: K10R8L
script.js:12487 [ANIMATION DEBUG] - state.gameOver: false
script.js:12488 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12489 [ANIMATION DEBUG] - isFirstStateReceived: true
script.js:12490 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12491 [ANIMATION DEBUG] - state.mode: online
script.js:12492 [ANIMATION DEBUG] - window.animationsCompletedForGame: K10R8L
script.js:12493 [ANIMATION DEBUG] - needsAnimations: true
script.js:12497 [ANIMATION DEBUG] ✅ AVVIO ANIMAZIONI - Condizioni soddisfatte!
script.js:12523 [STARTUP] Mantengo nomi permanenti esistenti dal matchFound
script.js:12549 [PLAYER2 MONITOR] Observer installato PRIMA delle operazioni per catturare tutte le modifiche
script.js:12552 [STARTUP] PRIMA di updatePlayerAreaNames - player1: giggio player2: bruscolino
script.js:12553 [STARTUP] DEBUG player-info-box - Verifica elementi DOM esistenti
script.js:12554 [STARTUP] player1NameElement: <span class=​"player-name loaded current-player" style=​"border:​ 2px solid gold;​ border-radius:​ 4px;​">​giggio​</span>​
script.js:12555 [STARTUP] player2NameElement: <span class=​"player-name loaded">​bruscolino​</span>​
script.js:12558 [STARTUP] Controllo flag namesFinallySet: true
script.js:12560 [STARTUP] Nomi già definitivamente impostati, skip updatePlayerAreaNames
script.js:12567 [STARTUP] DOPO updatePlayerAreaNames - player1: giggio player2: bruscolino
script.js:12572 [DICE ANIMATION] Mostrando setup animazione dadi
script.js:12573 [DICE ANIMATION] isStarting: false
script.js:12574 [DICE ANIMATION] isOnlineMode: true
script.js:12575 [DICE ANIMATION] isFirstStateReceived: true
script.js:12579 [DICE ANIMATION] Prima chiamata a showGameSetup
script.js:3843 [GAME SETUP] showGameSetup() chiamato
script.js:3892 [GAME SETUP] Preparando game container per partita locale
script.js:3896 [GAME SETUP] Container già visibile, mantengo stato esistente
script.js:3953 [GAME SETUP] Classe ready-for-play già presente, salto l'aggiunta
script.js:3960 [GAME SETUP] Tabellone esistente trovato, aggiorno rendering
script.js:5310 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5323 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:3976 [GAME SETUP] Completato
script.js:12666 [INITIAL POSITION] Salvata posizione iniziale originale: a4
script.js:12746 [ANIMATION] Animazioni già completate per questo gioco, skip
script.js:11525 [PSN AUTHORIRATIVE] Registrazione mosse dal server prima di handleGameState
game-mode-manager.js:104 [GAME MODE] Processando stato online senza manager attivo (normale durante inizializzazione)
script.js:12166 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12169 [GAME STATE] -otOSPYYXNrIK0pdAAAr (black): Array(5)
script.js:12169 [GAME STATE] Mz2eoSO3Hwkb-jYCAAAc (white): Array(5)
script.js:12389 [PERMANENT NAMES] Nomi permanenti finali: {"-otOSPYYXNrIK0pdAAAr":"bruscolino","Mz2eoSO3Hwkb-jYCAAAc":"giggio"}
script.js:12411 [PERMANENT COLORS] Colori permanenti finali: {"-otOSPYYXNrIK0pdAAAr":"black","Mz2eoSO3Hwkb-jYCAAAc":"white"}
psn-unified.js:109 [PSN API] Aggiornamento handSize dai dati del server
psn-unified.js:122 [PSN API] Aggiornato handSize nero: 5
psn-unified.js:119 [PSN API] Aggiornato handSize bianco: 5
script.js:12483 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12484 [ANIMATION DEBUG] - isStarting: false
script.js:12485 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12486 [ANIMATION DEBUG] - state.gameId: K10R8L
script.js:12487 [ANIMATION DEBUG] - state.gameOver: false
script.js:12488 [ANIMATION DEBUG] - isSetupAnimating: true
script.js:12489 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12490 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12491 [ANIMATION DEBUG] - state.mode: online
script.js:12492 [ANIMATION DEBUG] - window.animationsCompletedForGame: K10R8L
script.js:12493 [ANIMATION DEBUG] - needsAnimations: false
script.js:14312 [UI UPDATE] Nome avversario da state.playerNames: bruscolino
script.js:14351 [UPDATE UI] NESSUN originalCurrentPlayerId disponibile - né nello stato né da preservare
script.js:14358 [UPDATE UI] Preservata posizione iniziale originale: a4
script.js:13545 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13611 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13612 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13668 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14377 [DEBUG] Vantaggio calcolato: 50%
script.js:15817 Tentativo di forzare rendering rating...
script.js:15844 Ratings dopo init: Object
script.js:15853 Aggiornamento avatar player1: Mz2eoSO3Hwkb-jYCAAAc
script.js:15859 Aggiornamento avatar player2: -otOSPYYXNrIK0pdAAAr
script.js:14449 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
script.js:14452 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
script.js:14469 [UPDATE UI FINAL] NESSUN originalCurrentPlayerId disponibile - né nello stato né da preservare
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:14592 [ONLINE UI] ERRORE: player1Data o player2Data non disponibili
updateGameUI @ script.js:14592
script.js:14593 [ONLINE UI] state.players: Object
updateGameUI @ script.js:14593
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 0
player-names-protection.js:289 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: giggio
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: bruscolino
player-names-protection.js:123 [NAMES PROTECTION] Protezione attivata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: giggio
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: bruscolino
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 0
player-names-protection.js:289 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: giggio
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: bruscolino
player-names-protection.js:123 [NAMES PROTECTION] Protezione attivata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: giggio
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: bruscolino
script.js:13043 [TURN TIMER] Observer già attivato, evito chiamata ricorsiva a updateGameStateWithTurnTimer
script.js:12986 [SETUP ANIMATION] Animazione terminata - drag and drop sarà abilitato da animatePlayerNames()
script.js:14303 [UI UPDATE] Nome avversario da variabile globale: bruscolino
script.js:14351 [UPDATE UI] NESSUN originalCurrentPlayerId disponibile - né nello stato né da preservare
script.js:14358 [UPDATE UI] Preservata posizione iniziale originale: a4
script.js:13545 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13611 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
script.js:13612 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13668 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14377 [DEBUG] Vantaggio calcolato: 50%
script.js:15777 [HISTORY] Aggiunta mossa #1 alla cronologia
script.js:15817 Tentativo di forzare rendering rating...
script.js:15844 Ratings dopo init: Object
script.js:15853 Aggiornamento avatar player1: Mz2eoSO3Hwkb-jYCAAAc
script.js:15859 Aggiornamento avatar player2: -otOSPYYXNrIK0pdAAAr
script.js:14449 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
script.js:14452 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
script.js:14469 [UPDATE UI FINAL] NESSUN originalCurrentPlayerId disponibile - né nello stato né da preservare
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:14592 [ONLINE UI] ERRORE: player1Data o player2Data non disponibili
updateGameUI @ script.js:14592
script.js:14593 [ONLINE UI] state.players: Object
updateGameUI @ script.js:14593
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 0
player-names-protection.js:289 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: giggio
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: bruscolino
player-names-protection.js:123 [NAMES PROTECTION] Protezione attivata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: giggio
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: bruscolino
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 0
player-names-protection.js:289 [NAMES PROTECTION] Rilevato tentativo di render mano vuota, attivo protezione immediata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: giggio
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: bruscolino
player-names-protection.js:123 [NAMES PROTECTION] Protezione attivata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: giggio
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: bruscolino
script.js:13043 [TURN TIMER] Observer già attivato, evito chiamata ricorsiva a updateGameStateWithTurnTimer
psn-unified.js:2908 [PSN] Controllo periodico: Ripristino notazione esistente
