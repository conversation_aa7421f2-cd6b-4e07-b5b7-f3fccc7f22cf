/**
 * Gestione delle transizioni fluide tra pagine
 * Elimina i glitch visibili durante il caricamento tra dashboard.html e index.html
 */

// Crea l'elemento per la transizione di pagina
document.addEventListener('DOMContentLoaded', function() {
    // Creiamo l'elemento di transizione
    const transitionElement = document.createElement('div');
    transitionElement.className = 'page-transition';
    document.body.appendChild(transitionElement);
    
    // Impostiamo il corpo come caricato (rimuove il nascondimento iniziale)
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 50);

    // Gestione dei link per la transizione
    setupLinkTransitions();
});

/**
 * Configura tutti i link per usare animazioni di transizione
 */
function setupLinkTransitions() {
    // Intercetta tutti i link interni e i logo con classe home-link
    document.addEventListener('click', function(e) {
        // Gestione click sui loghi (sia nella sidebar che nella game-sidebar)
        if (e.target.classList.contains('logo-image') && e.target.classList.contains('home-link')) {
            e.preventDefault();
            
            // Attiva l'animazione di transizione
            const transitionElement = document.querySelector('.page-transition');
            transitionElement.classList.add('active');
            
            // Effetto di fade-out sulla pagina corrente
            const homepage = document.getElementById('homepage');
            if (homepage) {
                homepage.classList.add('transition-out');
            }
            
            // Attendi il completamento dell'animazione
            setTimeout(() => {
                window.location.href = '/index.html';
            }, 300);
            return;
        }
        
        // Controlla se è un link o un elemento dentro un link
        const linkElement = e.target.closest('a');
        
        if (!linkElement) return;
        
        // Se è un link a index.html o dashboard.html
        const href = linkElement.getAttribute('href');
        if (!href) return;
        
        // Se è un link esterno, non applichiamo la transizione
        if (href.startsWith('http') && !href.includes(window.location.hostname)) return;
        
        // Se è un link a una di queste pagine
        if (href === '/' || 
            href === '/index.html' || 
            href === '/dashboard.html' || 
            href === 'index.html' || 
            href === 'dashboard.html' || 
            href.startsWith('/?action=') ||
            href.startsWith('index.html?action=') ||
            href.endsWith('dashboard')) {
            
            e.preventDefault();
            
            // Attiva l'animazione di transizione
            const transitionElement = document.querySelector('.page-transition');
            transitionElement.classList.add('active');
            
            // Effetto di fade-out sulla pagina corrente
            const homepage = document.getElementById('homepage');
            if (homepage) {
                homepage.classList.add('transition-out');
            }
            
            // Attendi il completamento dell'animazione
            setTimeout(() => {
                window.location.href = href;
            }, 300);
        }
    });

    // Intercetta i pulsanti specifici per la navigazione
    const navigationButtons = [
        'play-online-button',
        // 'new-game-button', // Rimosso perché non naviga, ma mostra un pannello interno
        'training-button',
        // 'start-local-button', // Rimosso perché non naviga, ma mostra un pannello interno
        'login-button',
        'register-button',
        'logout-button'
    ];
    
    navigationButtons.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            button.addEventListener('click', function(e) {
                // Se il pulsante naviga a una pagina specifica (molti di questi hanno già
                // handler JS che impostano window.location.href)
                const transitionElement = document.querySelector('.page-transition');
                transitionElement.classList.add('active');
                
                // Effetto di fade-out sulla pagina corrente
                if (document.getElementById('homepage')) {
                    document.getElementById('homepage').classList.add('transition-out');
                }
                
                // Per i pulsanti che non hanno già un handler, non preveniamo il default
                // perché vogliamo che eseguano la loro azione normale
            });
        }
    });
}

// Gestione del caricamento iniziale della pagina
window.addEventListener('load', function() {
    // Gestisce la transizione quando si arriva sulla pagina
    // Questo nasconde qualsiasi glitch durante la costruzione iniziale DOM
    document.body.classList.add('loaded');

    // Nasconde la transizione se era attiva
    const transitionElement = document.querySelector('.page-transition');
    setTimeout(() => {
        if (transitionElement) {
            transitionElement.classList.remove('active');
        }
    }, 50);

    // Rimuove l'effetto di transizione uscente se era attivo
    const homepage = document.getElementById('homepage');
    if (homepage) {
        homepage.classList.remove('transition-out');
        homepage.classList.add('transition-in');
    }
});

// Gestione specifica per homelogged per evitare schermata nera
window.addEventListener('DOMContentLoaded', function() {
    // Se siamo nella pagina homelogged, mostra immediatamente il contenuto
    if (window.location.pathname.includes('home-logged') ||
        document.querySelector('.skemino-main_wrapper')) {

        // Rimuovi immediatamente la visibility hidden per homelogged
        document.body.classList.add('loaded');

        // Nasconde eventuali transizioni attive
        const transitionElement = document.querySelector('.page-transition');
        if (transitionElement) {
            transitionElement.classList.remove('active');
        }
    }
});

// Gestione dello scaricamento della pagina
window.addEventListener('beforeunload', function() {
    // Mostra la transizione (opzionale)
    document.querySelector('.page-transition').classList.add('active');
    
    // Imposta il corpo come non caricato (opzionale)
    document.body.classList.remove('loaded');
});