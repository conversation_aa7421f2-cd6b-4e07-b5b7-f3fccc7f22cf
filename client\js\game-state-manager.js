// Sistema di conservazione dello stato del gioco
const GAME_STATE_KEY = 'skemino_game_state';

function saveGameState(state) {
    console.log('[STATE] Salvando stato:', state);
    sessionStorage.setItem(GAME_STATE_KEY, JSON.stringify(state));
}

function loadGameState() {
    const saved = sessionStorage.getItem(GAME_STATE_KEY);
    if (saved) {
        try {
            const state = JSON.parse(saved);
            console.log('[STATE] Stato caricato:', state);
            return state;
        } catch (e) {
            console.error('[STATE] Errore nel parsing dello stato:', e);
            sessionStorage.removeItem(GAME_STATE_KEY);
        }
    }
    return null;
}

function clearGameState() {
    console.log('[STATE] Pulizia stato');
    sessionStorage.removeItem(GAME_STATE_KEY);
}

// Controlla sessionStorage e parametri URL per interfaccia completa
(function() {
    // Prima controlla sessionStorage per evitare glitch nell'URL
    let skipAnimations = false;
    let fullInterface = false;
    let fromSessionStorage = false;
    
    const sessionData = sessionStorage.getItem('skemino_full_interface');
    if (sessionData) {
        try {
            const parsed = JSON.parse(sessionData);
            // Verifica che i dati non siano troppo vecchi (max 30 secondi)
            if (Date.now() - parsed.timestamp < 30000) {
                skipAnimations = parsed.skipAnimations || false;
                fullInterface = parsed.fullInterface || false;
                fromSessionStorage = true;
                // Pulisci immediatamente per evitare riutilizzo
                sessionStorage.removeItem('skemino_full_interface');
                console.log('[STATE] Dati caricati da sessionStorage e rimossi');
            } else {
                console.log('[STATE] Dati sessionStorage scaduti, ignoro');
                sessionStorage.removeItem('skemino_full_interface');
            }
        } catch (e) {
            console.error('[STATE] Errore parsing sessionStorage:', e);
            sessionStorage.removeItem('skemino_full_interface');
        }
    }
    
    // Se non trovati in sessionStorage, controlla parametri URL (compatibilità)
    if (!fromSessionStorage) {
        const urlParams = new URLSearchParams(window.location.search);
        skipAnimations = urlParams.get('skipAnimations') === 'true';
        fullInterface = urlParams.get('fullInterface') === 'true';
    }
    
    const savedState = loadGameState();

    console.log('[STATE] Controllo iniziale:');
    console.log('[STATE] - skipAnimations:', skipAnimations);
    console.log('[STATE] - fullInterface:', fullInterface);
    console.log('[STATE] - fromSessionStorage:', fromSessionStorage);
    console.log('[STATE] - savedState:', savedState);

    // Determina se mostrare l'interfaccia completa
    const shouldShowFullInterface = (skipAnimations && fullInterface) ||
                                  (savedState && savedState.fullInterface);

    console.log('[STATE] - shouldShowFullInterface:', shouldShowFullInterface);

    // Se non c'è una condizione specifica ma siamo in game.html senza una partita attiva,
    // assumiamo che vogliamo l'interfaccia completa (comportamento di default)
    const hasActiveGame = sessionStorage.getItem('gameData') ||
                         localStorage.getItem('currentGameId') ||
                         document.querySelector('.game-active-indicator');

    const shouldDefaultToFullInterface = !shouldShowFullInterface && !hasActiveGame;

    console.log('[STATE] - hasActiveGame:', !!hasActiveGame);
    console.log('[STATE] - shouldDefaultToFullInterface:', shouldDefaultToFullInterface);

    const finalShouldShow = shouldShowFullInterface || shouldDefaultToFullInterface;

    if (finalShouldShow) {
        if (savedState && savedState.fullInterface) {
            console.log('[GAME] Stato salvato rilevato: ripristino interfaccia completa');
        } else if (fromSessionStorage) {
            console.log('[GAME] Dati da sessionStorage: attivo interfaccia completa');
        } else if (shouldDefaultToFullInterface) {
            console.log('[GAME] Nessuna partita attiva: attivo interfaccia completa di default');
        } else {
            console.log('[GAME] Parametri URL rilevati: attivo interfaccia completa');
        }

        // Mostra immediatamente l'interfaccia completa
        document.addEventListener('DOMContentLoaded', function() {
            console.log('[GAME] DOM caricato, mostro interfaccia completa');

            // Nascondi elementi di animazione
            const diceOverlay = document.getElementById('dice-animation-overlay');
            const diceArea = document.getElementById('dice-animation-area');
            const setupAnimation = document.getElementById('setup-animation');

            if (diceOverlay) diceOverlay.style.display = 'none';
            if (diceArea) diceArea.style.display = 'none';
            if (setupAnimation) setupAnimation.style.display = 'none';

            // Mostra il container del gioco
            const gameContainer = document.getElementById('game-container');
            if (gameContainer) {
                gameContainer.style.display = 'grid';
                gameContainer.classList.add('ready-for-play');
                console.log('[GAME] Game container mostrato con interfaccia completa');
            }

            // Crea il tabellone di gioco
            setTimeout(() => {
                if (typeof createGameBoard === 'function') {
                    createGameBoard();
                    console.log('[GAME] Tabellone di gioco creato');
                } else {
                    console.warn('[GAME] Funzione createGameBoard non disponibile');
                }

                // Crea anche gli slot per le mani dei giocatori
                if (typeof createHandSlots === 'function') {
                    createHandSlots();
                    console.log('[GAME] Slot mani giocatori creati');
                }
            }, 100);

            // Imposta nomi giocatori di default
            const player1Name = document.querySelector('#player1-area .player-name');
            const player2Name = document.querySelector('#player2-area .player-name');

            if (player1Name) player1Name.textContent = 'Giocatore 1';
            if (player2Name) player2Name.textContent = 'Giocatore 2';

            // Salva sempre lo stato dell'interfaccia completa
            const stateToSave = {
                fullInterface: true,
                activeTab: 'nuova-partita',
                timestamp: Date.now(),
                source: savedState ? 'restored' : (fromSessionStorage ? 'sessionStorage' : (shouldDefaultToFullInterface ? 'default' : 'url'))
            };
            saveGameState(stateToSave);
            console.log('[STATE] Stato salvato:', stateToSave);

            // Attiva il tab "Nuova Partita" e disabilita gli altri
            setTimeout(() => {
                // Rimuovi active da tutti i tab
                document.querySelectorAll('.sidebar-tabs .tab').forEach(tab => {
                    tab.classList.remove('active');
                    // Disabilita tutti i tab tranne "nuova-partita"
                    if (tab.getAttribute('data-tab') !== 'nuova-partita') {
                        tab.style.pointerEvents = 'none';
                        tab.style.opacity = '0.5';
                        tab.style.cursor = 'not-allowed';
                    }
                });

                // Nascondi tutti i contenuti dei tab
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                // Attiva solo il tab "Nuova Partita"
                const nuovaPartitaTab = document.querySelector('.tab[data-tab="nuova-partita"]');
                const nuovaPartitaContent = document.getElementById('tab-nuova-partita');

                if (nuovaPartitaTab) {
                    nuovaPartitaTab.classList.add('active');
                    console.log('[GAME] Tab "Nuova Partita" attivato');
                }

                if (nuovaPartitaContent) {
                    nuovaPartitaContent.classList.add('active');
                    console.log('[GAME] Contenuto "Nuova Partita" mostrato');
                }

                // Aggiungi event listener al pulsante "Nuova Partita"
                const startNewGameButton = document.getElementById('start-new-game-button');
                if (startNewGameButton) {
                    startNewGameButton.addEventListener('click', function() {
                        console.log('[GAME] Pulsante Nuova Partita cliccato');

                        // Ottieni le opzioni selezionate
                        const gameMode = document.getElementById('game-mode-select')?.value || 'online';
                        const timeLimit = document.getElementById('time-select')?.value || 'none';

                        console.log('[GAME] Modalità:', gameMode, 'Tempo:', timeLimit);

                        // Avvia sempre il matchmaking online (unica opzione disponibile)
                        startOnlineMatchmaking();
                    });
                }

                // Funzione per avviare il matchmaking online
                function startOnlineMatchmaking() {
                    console.log('[MATCHMAKING] Avvio matchmaking online');

                    // Verifica se l'utente è autenticato
                    if (!localStorage.getItem('loggedInUser') && (!window.authUtils || !window.authUtils.isLoggedIn())) {
                        const messageElement = document.getElementById('game-message-nuova-partita');
                        if (messageElement) {
                            messageElement.textContent = 'Devi essere loggato per giocare online';
                            messageElement.style.color = '#f44336';
                        }
                        return;
                    }

                    // Prima di mostrare il modal, verifica che il socket sia connesso
                    if (!window.socket || !window.socket.connected) {
                        if (typeof initializeSocket === 'function') {
                            console.log('[MATCHMAKING] Socket non connesso, inizializzazione...');
                            initializeSocket();
                        }
                    } else {
                        console.log('[MATCHMAKING] Socket già connesso');

                        // Verifica che il socket abbia il token di autenticazione
                        const token = localStorage.getItem('token');
                        if (token && (!window.socket.auth || !window.socket.auth.token)) {
                            console.log('[MATCHMAKING] Socket senza token, aggiornamento auth...');
                            window.socket.auth = { token: token };
                            window.socket.disconnect();
                            window.socket.connect();
                        }
                    }

                    // Mostra il modal di matchmaking
                    const matchmakingModal = document.getElementById('matchmaking-modal');
                    if (matchmakingModal) {
                        matchmakingModal.style.display = 'flex';

                        // Attendi un momento per la connessione del socket
                        setTimeout(() => {
                            if (window.socket && window.socket.connected) {
                                console.log('[MATCHMAKING] Socket connesso, invio findMatch');

                                // Rimuovi eventuali handler precedenti e aggiungi il nuovo
                                window.socket.off('matchFound');
                                window.socket.on('matchFound', (data) => {
                                    console.log('[MATCHMAKING] Match trovato!', data);

                                    // Chiudi il modal
                                    matchmakingModal.style.display = 'none';

                                    // Salva i dati della partita
                                    if (data.gameId) {
                                        console.log('[MATCHMAKING] Salvando dati partita con gameId:', data.gameId);
                                        sessionStorage.setItem('gameData', JSON.stringify(data));

                                        // Pulisci lo stato dell'interfaccia completa
                                        clearGameState();
                                        console.log('[MATCHMAKING] Stato interfaccia completa pulito');

                                        // Riabilita tutti i tab della sidebar
                                        setTimeout(() => {
                                            enableAllSidebarTabs();
                                            switchToGiocaTab();
                                            console.log('[MATCHMAKING] Passaggio al tab Gioca completato');
                                        }, 500);

                                        // Emetti evento di inizio partita
                                        window.dispatchEvent(new CustomEvent('gameStarted', {
                                            detail: {
                                                type: 'online',
                                                gameId: data.gameId,
                                                opponent: data.opponent
                                            }
                                        }));

                                        // RIMOSSO: Non più necessario ricaricamento pagina
                                        // Il flusso delle animazioni procederà automaticamente attraverso handleMatchFound() in multiplayer.js
                                        console.log('[MATCHMAKING] Match trovato - il flusso delle animazioni inizierà automaticamente senza refresh');
                                    } else {
                                        console.error('[MATCHMAKING] Nessun gameId ricevuto');
                                    }
                                });

                                const userData = window.authUtils ? window.authUtils.getCurrentUser() : null;
                                const requestData = {
                                    rating: userData?.rating || 1000,
                                    username: userData?.username || 'Giocatore'
                                };

                                console.log('[MATCHMAKING] Invio findMatch con dati:', requestData);
                                window.socket.emit('findMatch', requestData);
                            } else {
                                console.error('[MATCHMAKING] Socket non connesso');
                                matchmakingModal.style.display = 'none';
                                const messageElement = document.getElementById('game-message-nuova-partita');
                                if (messageElement) {
                                    messageElement.textContent = 'Errore di connessione al server';
                                    messageElement.style.color = '#f44336';
                                }
                            }
                        }, 500);
                    }
                }

                // Funzione per riabilitare tutti i tab della sidebar
                function enableAllSidebarTabs() {
                    console.log('[SIDEBAR] Riabilitazione di tutti i tab');
                    document.querySelectorAll('.sidebar-tabs .tab').forEach(tab => {
                        // Riabilita tutti i tab
                        tab.style.pointerEvents = 'auto';
                        tab.style.opacity = '1';
                        tab.style.cursor = 'pointer';
                        console.log('[SIDEBAR] Tab riabilitato:', tab.getAttribute('data-tab'));
                    });
                }

                // Funzione per passare al tab "Gioca"
                function switchToGiocaTab() {
                    console.log('[SIDEBAR] Passaggio al tab Gioca');

                    // Rimuovi active da tutti i tab
                    document.querySelectorAll('.sidebar-tabs .tab').forEach(tab => {
                        tab.classList.remove('active');
                    });

                    // Nascondi tutti i contenuti dei tab
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });

                    // Attiva il tab "Gioca"
                    const giocaTab = document.querySelector('.tab[data-tab="gioca"]');
                    const giocaContent = document.getElementById('tab-gioca');

                    if (giocaTab) {
                        giocaTab.classList.add('active');
                        console.log('[SIDEBAR] Tab "Gioca" attivato');
                    } else {
                        console.warn('[SIDEBAR] Tab "Gioca" non trovato');
                    }

                    if (giocaContent) {
                        giocaContent.classList.add('active');
                        console.log('[SIDEBAR] Contenuto "Gioca" mostrato');
                    } else {
                        console.warn('[SIDEBAR] Contenuto "Gioca" non trovato');
                    }
                }

                // Gestione del pulsante di cancellazione del matchmaking
                const cancelMatchmakingButton = document.getElementById('cancel-matchmaking');
                if (cancelMatchmakingButton) {
                    cancelMatchmakingButton.addEventListener('click', function() {
                        const matchmakingModal = document.getElementById('matchmaking-modal');
                        if (matchmakingModal) {
                            matchmakingModal.style.display = 'none';
                        }

                        // Cancella la ricerca se il socket è connesso
                        if (window.socket && window.socket.connected) {
                            window.socket.emit('cancelMatchmaking');
                        }
                    });
                }

                // Aggiungi listener per salvare le opzioni selezionate
                const gameModeSelect = document.getElementById('game-mode-select');
                const timeSelect = document.getElementById('time-select');

                if (gameModeSelect) {
                    gameModeSelect.addEventListener('change', function() {
                        const currentState = loadGameState();
                        if (currentState) {
                            currentState.gameMode = this.value;
                            saveGameState(currentState);
                        }
                    });
                }

                if (timeSelect) {
                    timeSelect.addEventListener('change', function() {
                        const currentState = loadGameState();
                        if (currentState) {
                            currentState.timeLimit = this.value;
                            saveGameState(currentState);
                        }
                    });
                }

                // Ripristina le opzioni salvate se esistono
                const currentState = loadGameState();
                if (currentState) {
                    if (currentState.gameMode && gameModeSelect) {
                        gameModeSelect.value = currentState.gameMode;
                    }
                    if (currentState.timeLimit && timeSelect) {
                        timeSelect.value = currentState.timeLimit;
                    }
                }
            }, 200);

            // Pulisci l'URL dai parametri solo se presenti nell'URL
            if (!fromSessionStorage && (skipAnimations && fullInterface)) {
                console.log('[STATE] Pulizia parametri URL');
                window.history.replaceState({}, document.title, '/game');
            } else if (fromSessionStorage) {
                console.log('[STATE] Dati da sessionStorage, URL già pulito');
            }
        });
    } else {
        // Solo se c'è una partita attiva, pulisci lo stato dell'interfaccia completa
        if (hasActiveGame) {
            console.log('[STATE] Partita attiva rilevata, pulizia stato interfaccia completa');
            clearGameState();
        } else {
            console.log('[STATE] Nessuna partita attiva, mantengo possibilità di interfaccia completa');
            // Non pulire lo stato - potrebbe essere utile per il prossimo accesso
        }
    }

    // Listener per pulire lo stato quando si naviga via dalla pagina
    window.addEventListener('beforeunload', function() {
        // Non pulire lo stato se stiamo ricaricando la pagina
        // Lo stato verrà pulito solo quando si naviga effettivamente via
        const savedState = loadGameState();
        if (savedState && savedState.fullInterface) {
            // Mantieni lo stato per il reload
            console.log('[STATE] Mantenendo stato per reload');
        } else {
            clearGameState();
        }
    });

    // Listener per pulire lo stato quando si inizia una partita normale
    window.addEventListener('gameStarted', function(event) {
        console.log('[STATE] Partita iniziata, pulizia stato interfaccia completa');
        console.log('[STATE] Dettagli partita:', event.detail);
        clearGameState();
    });

    // Listener per quando viene distribuita la prima carta (indica inizio partita)
    window.addEventListener('firstCardDealt', function() {
        console.log('[STATE] Prima carta distribuita, pulizia stato interfaccia completa');
        clearGameState();
    });

    // Listener per quando viene effettuata la prima mossa
    window.addEventListener('firstMoveCompleted', function() {
        console.log('[STATE] Prima mossa completata, pulizia stato interfaccia completa');
        clearGameState();
    });
})();

// Esposizione delle funzioni globalmente per compatibilità
window.saveGameState = saveGameState;
window.loadGameState = loadGameState;
window.clearGameState = clearGameState; 